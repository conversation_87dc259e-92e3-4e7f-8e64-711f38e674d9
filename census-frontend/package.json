{"name": "census-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"clsx": "^2.1.1", "framer-motion": "^12.23.15", "hammerjs": "^2.0.8", "jotai": "^2.14.0", "mapbox-gl": "^3.15.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-router-dom": "^7.9.1", "swr": "^2.3.6"}, "devDependencies": {"@eslint/js": "^9.35.0", "@types/hammerjs": "^2.0.46", "@types/mapbox-gl": "^3.4.1", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "@vitejs/plugin-react": "^5.0.2", "autoprefixer": "^10.4.21", "eslint": "^9.35.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.4.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.10", "typescript": "~5.8.3", "typescript-eslint": "^8.43.0", "vite": "^7.1.6"}}