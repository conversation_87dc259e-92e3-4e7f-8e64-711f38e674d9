// src/features/map/MapView.tsx
import { useEffect, useRef } from 'react';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';

const MAPBOX_STYLE =
  'mapbox://styles/uplandme/cmet29kke005401s2hy4b5lv9';

mapboxgl.accessToken = import.meta.env.VITE_MAPBOX_TOKEN ?? '';

export function MapView() {
  const containerRef = useRef<HTMLDivElement | null>(null);
  const mapRef = useRef<mapboxgl.Map | null>(null);
  const resizeObserverRef = useRef<ResizeObserver | null>(null);
  const animationFrameRef = useRef<number | null>(null);


useEffect(() => {
    if (!containerRef.current || mapRef.current) return;
    const container = containerRef.current;

    const map = new mapboxgl.Map({
      container,
      style: MAPBOX_STYLE,
      center: [0, 0],
      zoom: 1.3,
      pitch: 0,
      bearing: 0,
      projection: 'globe',
      attributionControl: false,
    });

    mapRef.current = map;

    const handleResize = () => map.resize();
    map.once('load', handleResize);

    resizeObserverRef.current = new ResizeObserver(handleResize);
    resizeObserverRef.current.observe(container);

const degreesPerSecond = -1.5; // negative = west → east spin
let lastTime = performance.now();

const animate = (time: number) => {
  const deltaSeconds = (time - lastTime) / 1000;
  lastTime = time;

  const currentBearing = map.getBearing();
  const nextBearing =
    (currentBearing + degreesPerSecond * deltaSeconds + 360) % 360;

  map.easeTo({
    center: [0, 0],
    zoom: 1.3,
    pitch: 0,
    bearing: nextBearing,
    duration: 0, // instantaneous update—keeps the continuous motion smooth
  });

  animationFrameRef.current = requestAnimationFrame(animate);
};

map.once('load', () => {
  animationFrameRef.current = requestAnimationFrame(animate);
});


    return () => {
      resizeObserverRef.current?.disconnect();
      resizeObserverRef.current = null;

      if (animationFrameRef.current !== null) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }

      map.remove();
      mapRef.current = null;
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className="absolute inset-0 h-full w-full"
    />
  );
}

export default MapView;
