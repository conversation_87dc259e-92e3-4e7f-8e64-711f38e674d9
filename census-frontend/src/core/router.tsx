import { createBrowserRouter } from 'react-router-dom';
import { HomePage } from '@/routes/home/<USER>';
import { LeaderboardPage } from '@/routes/leaderboard/LeaderboardPage';
import { NeighborhoodPage } from '@/routes/neighborhood/NeighborhoodPage';

export const router = createBrowserRouter([
  { path: '/', element: <HomePage /> },
  { path: '/leaderboard', element: <LeaderboardPage /> },
  { path: '/neighborhood/:id', element: <NeighborhoodPage /> },
]);
